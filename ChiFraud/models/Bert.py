# coding: UTF-8
"""
BERT模型实现
基于预训练的中文BERT模型进行欺诈检测分类任务
使用BERT的pooled output进行分类
"""

import torch
import torch.nn as nn
# from pytorch_pretrained_bert import BertModel, BertTokenizer
from pytorch_pretrained import BertModel, BertTokenizer


class Config(object):
    """
    BERT模型配置类
    包含所有训练和模型相关的超参数
    """

    def __init__(self, dataset, embedding):
        """
        初始化配置参数

        Args:
            dataset: 数据集名称
            embedding: 词向量类型（对BERT无效）
        """
        self.model_name = 'Bert'
        # 数据路径配置
        self.train_path = './dataset/ChiFraud_train.csv'      # 训练集路径
        self.dev_path = './dataset/ChiFraud_t2022.csv'       # 验证集路径（2022年数据）
        self.test_path = './dataset/ChiFraud_t2023.csv'      # 测试集路径（2023年数据）

        # 读取类别列表
        self.class_list = [x.strip() for x in open(
            './dataset/class.txt', encoding='utf-8').readlines()]

        # 模型保存路径
        self.save_path = dataset + './saved_dict/' + self.model_name + '.ckpt'
        # 设备配置：优先使用GPU
        self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')

        # 训练超参数
        self.require_improvement = 1000    # 早停：1000个batch没有改进就停止
        self.num_classes = len(self.class_list)  # 分类数量
        self.num_epochs = 50                # 最大训练轮数（减少以快速验证）
        self.batch_size = 16                # 批次大小（进一步减小以节省GPU内存）
        self.pad_size = 128                # 序列填充长度（减小以节省GPU内存）
        self.learning_rate = 5e-5          # 学习率（BERT推荐的学习率）

        # BERT相关配置
        self.bert_path = 'bert-base-chinese'  # 预训练BERT模型路径
        self.tokenizer = BertTokenizer.from_pretrained(self.bert_path)  # BERT分词器
        self.hidden_size = 768             # BERT隐藏层大小
        self.vocab_path = './new_vocab.txt'  # 词汇表路径（备用）
        self.log_path = dataset + '/log/' + self.model_name  # 日志路径


class Model(nn.Module):
    """
    BERT分类模型
    使用预训练的BERT模型 + 线性分类层
    """

    def __init__(self, config):
        """
        初始化模型

        Args:
            config: 配置对象
        """
        super(Model, self).__init__()
        # 加载预训练的BERT模型
        self.bert = BertModel.from_pretrained(config.bert_path)

        # 设置BERT参数可训练（微调）
        for param in self.bert.parameters():
            param.requires_grad = True

        # 分类头：将BERT的768维输出映射到类别数
        self.fc = nn.Linear(config.hidden_size, config.num_classes)

    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入元组 (token_ids, seq_len, attention_mask)

        Returns:
            out: 分类logits
        """
        context = x[0]  # token_ids
        mask = x[2]     # attention_mask

        # BERT前向传播，获取pooled output（CLS token的表示）
        _, pooled = self.bert(context, attention_mask=mask, output_all_encoded_layers=False)

        # 通过分类头得到最终输出
        out = self.fc(pooled)
        return out
