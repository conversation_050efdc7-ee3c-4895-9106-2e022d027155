# coding: UTF-8
"""
ChiFraud 中文欺诈检测系统主程序
该程序支持多种深度学习模型进行中文文本欺诈检测，包括：
TextCNN, FastText, TextRCNN, Transformer, Bert, Chinese_Bert
"""

import time
import torch
import numpy as np
from train_eval import train, init_network, predict
from importlib import import_module
import argparse

# 创建命令行参数解析器
parser = argparse.ArgumentParser(description='Chinese Text Detection')
parser.add_argument('--model', type=str, required=True, help='choose a model: TextCNN, FastText, TextRCNN, Transformer, Bert, Chinese_Bert')
parser.add_argument('--embedding', default='random', type=str, help='random or pre_trained')
parser.add_argument('--word', default=False, type=bool, help='True for word, False for char')
parser.add_argument('--mode', default='train', type=str, help='train or test')
args = parser.parse_args()

if __name__ == '__main__':
    # 设置数据集名称
    dataset = 'ChiFraud'
    model_name = args.model
    embedding = args.embedding

    # 根据不同模型选择对应的工具函数
    # 不同模型需要不同的数据预处理方式
    if model_name == 'FastText':
        # FastText模型使用专门的工具函数
        from utils_fasttext import build_dataset, build_iterator, get_time_dif
        embedding = 'random'  # FastText使用随机初始化的词向量
    elif model_name == 'Bert':
        # BERT模型使用专门的工具函数，包含tokenizer等
        from utils_bert import build_dataset, build_iterator, get_time_dif
        embedding = 'random'  # BERT有预训练权重，这里的embedding参数不影响
    elif model_name == 'Chinese_Bert':
        # 中文BERT模型使用专门的工具函数
        from utils_chinesebert import build_dataset, build_iterator, get_time_dif
        embedding = 'random'
    else:
        # 其他模型（TextCNN, Transformer等）使用通用工具函数
        from utils import build_dataset, build_iterator, get_time_dif

    # 动态导入指定的模型模块
    x = import_module('models.' + model_name)
    # 创建模型配置对象
    config = x.Config(dataset, embedding)

    # 设置随机种子，确保实验结果可复现
    np.random.seed(1)
    torch.manual_seed(1)
    torch.cuda.manual_seed_all(1)
    torch.backends.cudnn.deterministic = True

    # 开始数据加载
    start_time = time.time()
    print("Loading data...")
    # 构建数据集：词汇表、训练集、验证集、测试集
    vocab, train_data, dev_data, test_data = build_dataset(config, args.word)
    # 创建数据迭代器，用于批量加载数据
    train_iter = build_iterator(train_data, config)
    dev_iter = build_iterator(dev_data, config)
    test_iter = build_iterator(test_data, config)
    time_dif = get_time_dif(start_time)
    print("Time usage:", time_dif)

    # 模型训练/测试阶段
    config.n_vocab = len(vocab)  # 设置词汇表大小
    # 创建模型实例并移动到指定设备（GPU/CPU）
    model = x.Model(config).to(config.device)

    # 对于非预训练模型，需要初始化网络参数
    if model_name != 'Transformer' and model_name != 'Bert' and model_name != 'Chinese_Bert':
        init_network(model)
    print(model.parameters)

    # 根据模式选择训练或测试
    if args.mode == 'train':
        # 训练模式：进行模型训练，包括验证和测试
        train(config, model, train_iter, dev_iter, test_iter)
    elif args.mode == 'test':
        # 测试模式：加载已训练的模型进行预测
        model.load_state_dict(torch.load(config.save_path))
        predict(config, model, test_iter)
