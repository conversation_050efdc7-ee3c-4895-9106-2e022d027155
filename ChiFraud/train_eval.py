# coding: UTF-8
"""
训练和评估模块
包含模型训练、评估、预测等核心功能
支持TensorBoard可视化和详细的性能指标计算
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn import metrics
import time
from utils import get_time_dif
from tensorboardX import SummaryWriter


def init_network(model, method='xavier', exclude='embedding', seed=123):
    """
    初始化神经网络参数

    Args:
        model: 要初始化的模型
        method: 初始化方法 ('xavier', 'kaiming', 或其他)
        exclude: 排除的参数名关键字（如'embedding'）
        seed: 随机种子
    """
    for name, w in model.named_parameters():
        if exclude not in name:  # 跳过embedding层等特殊层
            if 'weight' in name:
                if method == 'xavier':
                    nn.init.xavier_normal_(w)  # Xavier正态分布初始化
                elif method == 'kaiming':
                    nn.init.kaiming_normal_(w)  # Kaiming正态分布初始化
                else:
                    nn.init.normal_(w)  # 标准正态分布初始化
            elif 'bias' in name:
                nn.init.constant_(w, 0)  # 偏置项初始化为0
            else:
                pass


def train(config, model, train_iter, dev_iter, test_iter):
    """
    模型训练主函数

    Args:
        config: 配置对象，包含训练参数
        model: 要训练的模型
        train_iter: 训练数据迭代器
        dev_iter: 验证数据迭代器
        test_iter: 测试数据迭代器
    """
    start_time = time.time()
    model.train()  # 设置模型为训练模式
    # 使用Adam优化器
    optimizer = torch.optim.Adam(model.parameters(), lr=config.learning_rate)

    total_batch = 0  # 总批次计数器
    dev_best_loss = float('inf')  # 记录验证集最佳损失
    last_improve = 0  # 上次改进的批次
    flag = False  # 早停标志
    # 创建TensorBoard日志记录器
    writer = SummaryWriter(log_dir=config.log_path + '/' + time.strftime('%m-%d_%H.%M', time.localtime()))

    # 开始训练循环
    for epoch in range(config.num_epochs):
        print('Epoch [{}/{}]'.format(epoch + 1, config.num_epochs))
        for i, (trains, labels) in enumerate(train_iter):
            # 前向传播
            outputs = model(trains)
            model.zero_grad()  # 清零梯度
            # 计算交叉熵损失
            loss = F.cross_entropy(outputs, labels)
            # 反向传播
            loss.backward()
            # 更新参数
            optimizer.step()

            # 每100个批次进行一次验证和日志记录
            if total_batch % 100 == 0:
                # 计算训练准确率
                true = labels.data.cpu()
                predic = torch.max(outputs.data, 1)[1].cpu()
                train_acc = metrics.accuracy_score(true, predic)
                # 在验证集上评估
                dev_acc, dev_loss = evaluate(config, model, dev_iter)

                # 如果验证损失改进，保存模型
                if dev_loss < dev_best_loss:
                    dev_best_loss = dev_loss
                    torch.save(model.state_dict(), config.save_path)
                    improve = '*'  # 标记改进
                    last_improve = total_batch
                else:
                    improve = ''

                # 打印训练进度
                time_dif = get_time_dif(start_time)
                msg = 'Iter: {0:>6},  Train Loss: {1:>5.2},  Train Acc: {2:>6.2%},  Val Loss: {3:>5.2},  Val Acc: {4:>6.2%},  Time: {5} {6}'
                print(msg.format(total_batch, loss.item(), train_acc, dev_loss, dev_acc, time_dif, improve))

                # 记录到TensorBoard
                writer.add_scalar("loss/train", loss.item(), total_batch)
                writer.add_scalar("loss/dev", dev_loss, total_batch)
                writer.add_scalar("acc/train", train_acc, total_batch)
                writer.add_scalar("acc/dev", dev_acc, total_batch)
                model.train()  # 重新设置为训练模式

            total_batch += 1
            # 早停机制：如果长时间没有改进，停止训练
            if total_batch - last_improve > config.require_improvement:
                print("No optimization for a long time, auto-stopping...")
                flag = True
                break
        if flag:
            break

    writer.close()  # 关闭TensorBoard记录器

    # 训练完成后进行最终评估
    print("Begin Evaluating!")
    calculate_results(config, model, dev_iter, mode='eval')
    print("Begin Testing!")
    calculate_results(config, model, test_iter, mode='test')


def calculate_results(config, model, test_iter, mode):
    """
    计算并显示模型在测试集上的详细结果

    Args:
        config: 配置对象
        model: 训练好的模型
        test_iter: 测试数据迭代器
        mode: 'eval' 或 'test'，决定使用哪个函数进行评估
    """
    # 加载最佳模型参数
    model.load_state_dict(torch.load(config.save_path))
    model.eval()  # 设置为评估模式
    start_time = time.time()

    # 根据模式选择评估函数
    if mode == 'eval':
        # 验证模式：使用evaluate函数
        test_acc, test_loss, test_report, test_confusion = evaluate(config, model, test_iter, test=True)
    elif mode == 'test':
        # 测试模式：使用predict函数，会保存预测结果
        test_acc, test_loss, test_report, test_confusion = predict(config, model, test_iter, test=True)

    # 打印结果
    msg = 'Test Loss: {0:>5.2},  Test Acc: {1:>6.2%}'
    print(msg.format(test_loss, test_acc))
    print("Precision, Recall and F1-Score...")
    print(test_report)  # 详细的分类报告
    print("Confusion Matrix...")
    print(test_confusion)  # 混淆矩阵
    time_dif = get_time_dif(start_time)
    print("Time usage:", time_dif)

def predict(config, model, data_iter, test=False):
    """
    模型预测函数，用于测试集预测
    会保存预测结果到文件，包含原文本、预测标签和概率分布

    Args:
        config: 配置对象
        model: 训练好的模型
        data_iter: 数据迭代器
        test: 是否返回详细测试结果

    Returns:
        准确率和损失，如果test=True还返回分类报告和混淆矩阵
    """
    model.eval()  # 设置为评估模式
    loss_total = 0
    predict_all = np.array([], dtype=int)  # 存储所有预测结果
    labels_all = np.array([], dtype=int)   # 存储所有真实标签
    probs_all = list()  # 存储所有预测概率

    with torch.no_grad():  # 禁用梯度计算以节省内存
        for texts, labels in data_iter:
            begin_time = time.time()
            outputs = model(texts)  # 模型前向传播
            print('time.....', time.time()-begin_time)  # 打印推理时间
            loss = F.cross_entropy(outputs, labels)
            loss_total += loss

            # 转换为numpy数组便于处理
            labels = labels.data.cpu().numpy()
            predic = torch.max(outputs.data, 1)[1].cpu().numpy()  # 获取预测类别
            probs = F.softmax(outputs.data, dim=1).cpu().tolist()  # 获取概率分布

            probs_all.extend(probs)
            labels_all = np.append(labels_all, labels)
            predict_all = np.append(predict_all, predic)

    # 读取原始测试数据并保存预测结果
    content_all = [line.strip() for line in open('./dataset/ChiFraud_t2023.csv').readlines()]
    predict_res_file = open('./result/ChiFraud_t2023.{}.res.txt'.format(config.model_name), 'w')
    for i in range(predict_all.shape[0]):
        # 格式：原文本 + 预测标签 + 概率分布
        line = content_all[i] + '\t' + str(predict_all[i]) + '\t' + ','.join([str(p) for p in probs_all[i]]) + '\n'
        predict_res_file.write(line)

    # 计算准确率
    acc = metrics.accuracy_score(labels_all, predict_all)
    if test:
        # 生成详细的分类报告和混淆矩阵
        report = metrics.classification_report(labels_all, predict_all, target_names=config.class_list, digits=4, labels=[0,1,2,3,4,5,6,7,8,9,10])
        confusion = metrics.confusion_matrix(labels_all, predict_all)
        return acc, loss_total / len(data_iter), report, confusion
    return acc, loss_total / len(data_iter)

def evaluate(config, model, data_iter, test=False):
    """
    模型评估函数，用于验证集评估
    会保存预测结果到文件，但不包含概率分布

    Args:
        config: 配置对象
        model: 训练好的模型
        data_iter: 数据迭代器
        test: 是否返回详细测试结果

    Returns:
        准确率和损失，如果test=True还返回分类报告和混淆矩阵
    """
    model.eval()  # 设置为评估模式
    loss_total = 0
    predict_all = np.array([], dtype=int)  # 存储所有预测结果
    labels_all = np.array([], dtype=int)   # 存储所有真实标签

    with torch.no_grad():  # 禁用梯度计算
        for texts, labels in data_iter:
            outputs = model(texts)  # 模型前向传播
            loss = F.cross_entropy(outputs, labels)
            loss_total += loss

            # 转换为numpy数组
            labels = labels.data.cpu().numpy()
            predic = torch.max(outputs.data, 1)[1].cpu().numpy()  # 获取预测类别
            labels_all = np.append(labels_all, labels)
            predict_all = np.append(predict_all, predic)

    # 读取原始验证数据并保存预测结果
    content_all = [line.strip() for line in open('./dataset/ChiFraud_t2022.csv').readlines()]
    predict_res_file = open('./result/ChiFraud_t2022.{}.res.txt'.format(config.model_name), 'w')
    for i in range(predict_all.shape[0]):
        # 格式：原文本 + 预测标签
        line = content_all[i] + '\t' + str(predict_all[i]) + '\n'
        predict_res_file.write(line)

    # 计算准确率
    acc = metrics.accuracy_score(labels_all, predict_all)
    if test:
        # 生成详细的分类报告和混淆矩阵（注意这里是10个类别，不包含第11个）
        report = metrics.classification_report(labels_all, predict_all, target_names=config.class_list, digits=4, labels=[0,1,2,3,4,5,6,7,8,9])
        confusion = metrics.confusion_matrix(labels_all, predict_all)
        return acc, loss_total / len(data_iter), report, confusion
    return acc, loss_total / len(data_iter)
