# coding: UTF-8
"""
BERT模型专用的数据处理工具
包含数据集构建、数据迭代器等功能
专门处理BERT模型需要的token化、padding、attention mask等
"""

import torch
from tqdm import tqdm
import time
from datetime import timedelta

# BERT特殊token定义
PAD, CLS = '[PAD]', '[CLS]'


def build_dataset(config, ues_word):
    """
    构建BERT模型的数据集

    Args:
        config: 配置对象，包含tokenizer和路径信息
        ues_word: 是否使用词级别（对BERT无效，因为BERT使用subword）

    Returns:
        vocab: 词汇表（对BERT为空）
        train: 训练数据
        dev: 验证数据
        test: 测试数据
    """

    def load_dataset(path, pad_size=32):
        """
        加载单个数据集文件

        Args:
            path: 数据文件路径
            pad_size: 填充长度

        Returns:
            contents: 处理后的数据列表，每个元素包含(token_ids, label, seq_len, mask)
        """
        contents = []
        with open(path, 'r', encoding='UTF-8') as f:
            for line in tqdm(f):
                lin = line.strip()
                if not lin:
                    continue
                # 解析标签和内容
                label, content = lin.split('\t')[0:2]
                if label == 'Label_id':  # 跳过表头
                    continue

                # 截断文本，为CLS和SEP预留位置
                content = content[:pad_size-2]
                # 使用BERT tokenizer进行token化
                token = config.tokenizer.tokenize(content)
                # 添加CLS token（BERT的起始token）
                token = [CLS] + token
                seq_len = len(token)
                mask = []
                # 将token转换为ID
                token_ids = config.tokenizer.convert_tokens_to_ids(token)

                # 进行padding和创建attention mask
                if pad_size:
                    if len(token) < pad_size:
                        # 短于pad_size：创建mask并padding
                        mask = [1] * len(token_ids) + [0] * (pad_size - len(token))
                        token_ids += ([0] * (pad_size - len(token)))
                    else:
                        # 长于pad_size：截断
                        mask = [1] * pad_size
                        token_ids = token_ids[:pad_size]
                        seq_len = pad_size
                contents.append((token_ids, int(label), seq_len, mask))
        return contents

    # 加载训练、验证、测试数据
    train = load_dataset(config.train_path, config.pad_size)
    dev = load_dataset(config.dev_path, config.pad_size)
    test = load_dataset(config.test_path, config.pad_size)
    vocab = list()  # BERT不需要额外的词汇表
    return vocab, train, dev, test


class DatasetIterater(object):
    """
    BERT数据集迭代器
    负责将数据按批次组织并转换为PyTorch张量
    """

    def __init__(self, batches, batch_size, device):
        """
        初始化数据迭代器

        Args:
            batches: 数据列表
            batch_size: 批次大小
            device: 设备（CPU/GPU）
        """
        self.batch_size = batch_size
        self.batches = batches
        self.n_batches = len(batches) // batch_size  # 完整批次数量
        self.residue = False  # 是否有剩余数据
        if len(batches) % self.n_batches != 0:
            self.residue = True
        self.index = 0  # 当前批次索引
        self.device = device

    def _to_tensor(self, datas):
        """
        将数据转换为PyTorch张量

        Args:
            datas: 批次数据列表

        Returns:
            (x, seq_len, mask): 输入数据元组
            y: 标签张量
        """
        # 提取token_ids
        x = torch.LongTensor([_[0] for _ in datas]).to(self.device)
        # 提取标签
        y = torch.LongTensor([_[1] for _ in datas]).to(self.device)
        # 提取序列长度
        seq_len = torch.LongTensor([_[2] for _ in datas]).to(self.device)
        # 提取attention mask
        mask = torch.LongTensor([_[3] for _ in datas]).to(self.device)
        return (x, seq_len, mask), y

    def __next__(self):
        """
        获取下一个批次
        """
        if self.residue and self.index == self.n_batches:
            # 处理最后一个不完整的批次
            batches = self.batches[self.index * self.batch_size: len(self.batches)]
            self.index += 1
            batches = self._to_tensor(batches)
            return batches

        elif self.index >= self.n_batches:
            # 所有批次已遍历完，重置索引并停止迭代
            self.index = 0
            raise StopIteration
        else:
            # 获取当前批次
            batches = self.batches[self.index * self.batch_size: (self.index + 1) * self.batch_size]
            self.index += 1
            batches = self._to_tensor(batches)
            return batches

    def __iter__(self):
        """返回迭代器自身"""
        return self

    def __len__(self):
        """返回总批次数量"""
        if self.residue:
            return self.n_batches + 1  # 包含剩余批次
        else:
            return self.n_batches


def build_iterator(dataset, config):
    """
    构建数据迭代器

    Args:
        dataset: 处理后的数据集
        config: 配置对象，包含batch_size和device信息

    Returns:
        DatasetIterater: 数据迭代器实例
    """
    iter = DatasetIterater(dataset, config.batch_size, config.device)
    return iter


def get_time_dif(start_time):
    """
    计算时间差

    Args:
        start_time: 开始时间

    Returns:
        timedelta: 格式化的时间差
    """
    end_time = time.time()
    time_dif = end_time - start_time
    return timedelta(seconds=int(round(time_dif)))
